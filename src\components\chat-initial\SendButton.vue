﻿<script setup>
import IconSend from '../icons/IconSend.vue';

defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
});

defineEmits(['send']);
</script>

<template>
  <div
    role="button"
    :aria-disabled="disabled"
    class="send-button"
    @click="!disabled && $emit('send')"
  >
    <div class="button-inner">
      <div class="icon-container">
        <IconSend />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.send-button {
  align-items: center;
  background: none 0% 0% / auto repeat scroll padding-box border-box rgb(214, 222, 232);
  border-radius: 16px;
  color: rgb(250, 250, 250);
  cursor: not-allowed;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  height: 32px;
  justify-content: center;
  min-width: 32px;
  white-space: nowrap;

  &:not([aria-disabled="true"]) {
    background: rgb(0, 122, 255);
    cursor: pointer;
  }
}

.button-inner {
  align-items: center;
  display: flex;
  height: 28px;
  justify-content: center;
  min-width: 28px;
}

.icon-container {
  display: flex;
  line-height: 0px;
  font-size: 16px;
  width: 16px;
  height: 16px;
}
</style>