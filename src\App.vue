<script setup>
import { computed, watch, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ChatInputBox from "@/components/chat-initial/ChatInputBox.vue";
import IconLogoDeepseek from "./components/icons/IconLogoDeepseek.vue";
import Sidebar from "@/components/sidebar/sidebar.vue";
import Chat from "./components/chat/chat.vue";
import Settings from "@/components/user/settings.vue";
import { useChatStore } from '@/store/chat';
import { chatApiService } from '@/services/apiService';

const route = useRoute();
const router = useRouter();
const chatStore = useChatStore();

const showSettings = ref(false);

// 计算当前是否在对话页面
const isInChatSession = computed(() => {
  return route.name === 'chat-session' && route.params.id;
});

// 获取当前对话ID
const currentChatId = computed(() => {
  return isInChatSession.value ? String(route.params.id) : null;
});

// 监听路由变化，更新store中的当前对话ID
watch(currentChatId, (newChatId) => {
  chatStore.setCurrentChatId(newChatId);
}, { immediate: true });

// 处理发送消息事件
const handleSendMessage = async (messageData) => {
  if (chatStore.isCurrentlySending) return;

  try {
    // 创建新对话
    const newChat = await chatApiService.createChat('新对话');

    // 立即更新store并跳转到对话页面
    chatStore.addChat({
      id: newChat.id,
      title: newChat.title || '新对话',
      created_at: newChat.creation_timestamp,
      updated_at: newChat.last_update_timestamp,
      entry_node_id: null // 初始为空，后续会更新
    });

    // 立即导航到新对话，并传递初始消息数据
    router.push({
      path: `/session/${newChat.id}`,
      query: {
        initialMessage: messageData.content,
        isDeepThinkActive: messageData.isDeepThinkActive.toString(),
        isWebSearchActive: messageData.isWebSearchActive.toString()
      }
    });

  } catch (error) {
    console.error('Failed to create chat:', error);
    // 可以添加错误提示
  }
};

// 处理打开设置模态框
const handleOpenSettings = () => {
  showSettings.value = true;
};
</script>

<template>
  <div id="root">
    <Sidebar @open-settings="handleOpenSettings" />

    <!-- 初始聊天界面 - 当没有选中对话时显示 -->
    <div v-if="!isInChatSession" class="chat-input-container">
      <div class="chat-header">
        <div class="logo-wrapper">
          <IconLogoDeepseek />
        </div>
        我是 DeepSeek，很高兴见到你！
      </div>
      <div class="chat-content">
        我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~
      </div>
      <ChatInputBox style="width: 100%;" :isSending="chatStore.isCurrentlySending" @send="handleSendMessage" />
    </div>

    <!-- 对话界面 - 当选中对话时显示 -->
    <div v-else class="chat-container">
      <Chat :activeChatId="currentChatId" />
    </div>

    <!-- 设置模态框 -->
    <Settings v-model="showSettings" />
  </div>
</template>

<style scoped>
#root {
  display: flex;
  height: 100%;
  width: 100%;

}

.chat-input-container, .chat-container {
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  width: 100%;
  font-size: 14px;
  font-family: DeepSeek-CJK-patch, Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif;
}

.chat-input-container {
   max-width: 800px;
}

.chat-header {
  align-items: center;
  justify-content: center;
  gap: 14px;
  display: flex;
  font-size: 24px;
  font-weight: 500;
  flex-direction: row;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}

.logo-wrapper {
  display: flex;
  line-height: 0px;
  left: auto;
  position: static;
  font-size: 60px;
  width: 60px;
  height: 60px;
}

.logo-icon {
  height: 60px;
  width: 60px;
}

.chat-content {
  color: rgb(64, 64, 64);
  font-size: 14px;
  margin: 8px 0px 20px;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}



header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>
