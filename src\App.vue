<script setup>
import { computed, watch, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ChatInputBox from "@/components/chat-initial/ChatInputBox.vue";
import IconLogoDeepseek from "./components/icons/IconLogoDeepseek.vue";
import Sidebar from "@/components/sidebar/sidebar.vue";
import Chat from "./components/chat/chat.vue";
import Settings from "@/components/user/settings.vue";
import { useChatStore } from '@/store/chat';
import { chatApiService } from '@/services/apiService';
import useToast from '@/components/toast/useToast';

const route = useRoute();
const router = useRouter();
const chatStore = useChatStore();
const toast = useToast();

const showSettings = ref(false);

// 计算当前是否在对话页面
const isInChatSession = computed(() => {
  return route.name === 'chat-session' && route.params.id;
});

// 获取当前对话ID
const currentChatId = computed(() => {
  return isInChatSession.value ? String(route.params.id) : null;
});

// 监听路由变化，更新store中的当前对话ID
watch(currentChatId, (newChatId) => {
  chatStore.setCurrentChatId(newChatId);
}, { immediate: true });

// 处理发送消息事件
const handleSendMessage = async (messageData) => {
  if (chatStore.isCurrentlySending) return;

  try {
    // 创建新对话
    const newChat = await chatApiService.createChat('新对话');

    // 立即更新store并跳转到对话页面
    chatStore.addChat({
      id: newChat.id,
      title: newChat.title || '新对话',
      created_at: newChat.creation_timestamp,
      updated_at: newChat.last_update_timestamp,
      entry_node_id: null // 初始为空，后续会更新
    });

    // 立即导航到新对话，并传递初始消息数据
    router.push({
      path: `/session/${newChat.id}`,
      query: {
        initialMessage: messageData.content,
        isDeepThinkActive: messageData.isDeepThinkActive.toString(),
        isWebSearchActive: messageData.isWebSearchActive.toString()
      }
    });

  } catch (error) {
    console.error('Failed to create chat:', error);
    // 可以添加错误提示
  }
};

// 处理打开设置模态框
const handleOpenSettings = () => {
  showSettings.value = true;
};

// Toast 测试函数
const testToastSuccess = () => {
  toast.success('操作成功！这是一条成功消息');
};

const testToastError = () => {
  toast.error('操作失败！请检查网络连接');
};

const testToastWarning = () => {
  toast.warning('请注意：这是一条警告消息');
};

const testToastInfo = () => {
  toast.info('这是一条信息提示');
};

const testToastCustom = () => {
  toast.show({
    type: 'success',
    message: '这是一条自定义消息，5秒后消失',
    duration: 5000,
    closable: true
  });
};
</script>

<template>
  <div id="root">
    <Sidebar @open-settings="handleOpenSettings" />

    <!-- 初始聊天界面 - 当没有选中对话时显示 -->
    <div v-if="!isInChatSession" class="chat-input-container">
      <div class="chat-header">
        <div class="logo-wrapper">
          <IconLogoDeepseek />
        </div>
        我是 DeepSeek，很高兴见到你！
      </div>
      <div class="chat-content">
        我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~
      </div>

      <!-- Toast 测试按钮 -->
      <div class="toast-test-buttons">
        <h3>Toast 组件测试</h3>
        <div class="button-group">
          <button @click="testToastSuccess" class="test-btn success">成功消息</button>
          <button @click="testToastError" class="test-btn error">错误消息</button>
          <button @click="testToastWarning" class="test-btn warning">警告消息</button>
          <button @click="testToastInfo" class="test-btn info">信息消息</button>
          <button @click="testToastCustom" class="test-btn custom">自定义消息</button>
        </div>
      </div>

      <ChatInputBox style="width: 100%;" :isSending="chatStore.isCurrentlySending" @send="handleSendMessage" />
    </div>

    <!-- 对话界面 - 当选中对话时显示 -->
    <div v-else class="chat-container">
      <Chat :activeChatId="currentChatId" />
    </div>

    <!-- 设置模态框 -->
    <Settings v-model="showSettings" />
  </div>
</template>

<style scoped>
#root {
  display: flex;
  height: 100%;
  width: 100%;

}

.chat-input-container, .chat-container {
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  width: 100%;
  font-size: 14px;
  font-family: DeepSeek-CJK-patch, Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif;
}

.chat-input-container {
   max-width: 800px;
}

.chat-header {
  align-items: center;
  justify-content: center;
  gap: 14px;
  display: flex;
  font-size: 24px;
  font-weight: 500;
  flex-direction: row;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}

.logo-wrapper {
  display: flex;
  line-height: 0px;
  left: auto;
  position: static;
  font-size: 60px;
  width: 60px;
  height: 60px;
}

.logo-icon {
  height: 60px;
  width: 60px;
}

.chat-content {
  color: rgb(64, 64, 64);
  font-size: 14px;
  margin: 8px 0px 20px;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}

.toast-test-buttons {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  width: 100%;
  max-width: 780px;
}

.toast-test-buttons h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 80px;
}

.test-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.test-btn.success {
  background-color: #52c41a;
  color: white;
}

.test-btn.success:hover {
  background-color: #389e0d;
}

.test-btn.error {
  background-color: #ff4d4f;
  color: white;
}

.test-btn.error:hover {
  background-color: #cf1322;
}

.test-btn.warning {
  background-color: #faad14;
  color: white;
}

.test-btn.warning:hover {
  background-color: #d48806;
}

.test-btn.info {
  background-color: #1890ff;
  color: white;
}

.test-btn.info:hover {
  background-color: #096dd9;
}

.test-btn.custom {
  background-color: #722ed1;
  color: white;
}

.test-btn.custom:hover {
  background-color: #531dab;
}



header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>
