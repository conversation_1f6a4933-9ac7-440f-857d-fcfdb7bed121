import { inject } from "vue";
import { MenuItem } from "./pop-menu.vue";

export const PopMenuSymbol = Symbol('PopMenu');
export interface IPopMenuHookReturn {
  toggle(items: MenuItem[], x: number, y: number): void;
  show(items: MenuItem[], x: number, y: number): void;
  hide(): void;
}

export default function usePopMenu() {
  console.log('inject');
  return inject(PopMenuSymbol) as IPopMenuHookReturn;
}