<template>
  <div 
    class="toast-item" 
    :class="[`toast-item--${item.type}`, { 'toast-item--closable': item.closable }]"
    role="alert"
    :aria-live="item.type === 'error' ? 'assertive' : 'polite'"
  >
    <div class="toast-item__icon" v-if="item.icon || defaultIcon">
      <component :is="item.icon || defaultIcon" />
    </div>
    <div class="toast-item__content">
      <div class="toast-item__message">{{ item.message }}</div>
    </div>
    <button 
      v-if="item.closable" 
      class="toast-item__close" 
      @click="$emit('close')"
      aria-label="关闭通知"
    >
      <span class="toast-item__close-icon">×</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType, type Component } from 'vue';

export interface ToastItem {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  closable?: boolean;
  icon?: Component;
}

const props = defineProps({
  item: {
    type: Object as PropType<ToastItem>,
    required: true
  }
});

const emit = defineEmits(['close']);

// 默认图标映射
const defaultIcon = computed(() => {
  const iconMap = {
    success: '✓',
    error: '✗',
    warning: '⚠',
    info: 'ℹ'
  };
  return iconMap[props.item.type];
});
</script>

<style lang="scss" scoped>
$toast-border-radius: 8px;
$toast-padding: 12px 16px;
$toast-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
$toast-font-size: 14px;
$toast-line-height: 1.4;
$toast-min-width: 300px;
$toast-max-width: 500px;

// 颜色变量
$success-color: #52c41a;
$success-bg: #f6ffed;
$success-border: #b7eb8f;

$error-color: #ff4d4f;
$error-bg: #fff2f0;
$error-border: #ffccc7;

$warning-color: #faad14;
$warning-bg: #fffbe6;
$warning-border: #ffe58f;

$info-color: #1890ff;
$info-bg: #f0f9ff;
$info-border: #91d5ff;

.toast-item {
  display: flex;
  align-items: flex-start;
  min-width: $toast-min-width;
  max-width: $toast-max-width;
  padding: $toast-padding;
  border-radius: $toast-border-radius;
  box-shadow: $toast-shadow;
  font-size: $toast-font-size;
  line-height: $toast-line-height;
  margin-bottom: 8px;
  border: 1px solid;
  transition: all 0.3s ease;

  &--success {
    background-color: $success-bg;
    border-color: $success-border;
    color: $success-color;
  }

  &--error {
    background-color: $error-bg;
    border-color: $error-border;
    color: $error-color;
  }

  &--warning {
    background-color: $warning-bg;
    border-color: $warning-border;
    color: $warning-color;
  }

  &--info {
    background-color: $info-bg;
    border-color: $info-border;
    color: $info-color;
  }

  &__icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 16px;
    font-weight: bold;
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__message {
    word-wrap: break-word;
    color: #333;
  }

  &__close {
    flex-shrink: 0;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-left: 8px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }

    &-icon {
      font-size: 18px;
      line-height: 1;
      color: #999;
    }
  }
}
</style>
