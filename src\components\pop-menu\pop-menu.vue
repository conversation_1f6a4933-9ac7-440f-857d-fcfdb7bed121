<template>
  <div role="menu" class="menu-container" @click.stop="">
    <div
      v-for="item in items"
      :key="item.id"
      class="menu-item"
      :style="{ color: item.color }"
      @click="handleClick(item)"
      tabindex="0"
      @keydown.enter.space="handleClick(item)"
    >
      <div class="menu-item-icon-wrapper" v-if="item.icon">
        <component
          :is="item.icon"
          class="menu-item-icon"
          aria-hidden="true"
         />
      </div>
      <span class="menu-item-title">{{ item.title }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type PropType, type Component, onMounted, onUnmounted } from 'vue';

const emit = defineEmits(['hide']);

export interface MenuItem {
  id: string | number; // Unique key for v-for
  icon?: Component;
  title: string;
  color?: string; // Optional: CSS color string
  onClick?: () => void; // Optional: Click handler function
  // Add any other properties you might need, e.g., disabled: boolean
}

// Define component props
const props = defineProps({
  items: {
    type: Array as PropType<MenuItem[]>,
    required: true,
    default: () => [] // Provide a default empty array
  }
});

// Handle click events
const handleClick = (item: MenuItem) => {
  if (item.onClick) {
    item.onClick();
  }
  // Optionally emit an event from the menu component as well
  // emit('item-click', item);
};

const handleWindowClick = (event: MouseEvent) => {
  emit('hide');
};
onMounted(() => {
  window.addEventListener('click', handleWindowClick);
});
onUnmounted(() => {
  window.removeEventListener('click', handleWindowClick);
});
</script>

<style lang="scss" scoped>
// Define some variables for consistency (optional but recommended)
$menu-background: rgb(255, 255, 255);
$menu-shadow: rgba(0, 0, 0, 0.12) 0px 8px 24px 0px;
$menu-border-radius: 12px;
$menu-padding: 4px;
$item-padding: 8px 16px 8px 8px;
$item-border-radius: 12px;
$default-text-color: rgb(64, 64, 64);
$icon-color: rgb(139, 139, 139); // Default icon color from original .DIV-2
$icon-size: 24px;
$icon-margin-right: 10px;
$hover-background: rgba(0, 0, 0, 0.04);
$transition-speed: 0.2s;
$transition-timing: cubic-bezier(0.4, 0, 0.2, 1);

.menu-container {
  background-color: $menu-background;
  border-radius: $menu-border-radius;
  box-shadow: $menu-shadow;
  color: $default-text-color;
  padding: $menu-padding;
  font-size: 14px; // Base font size
  line-height: 1.5; // Use relative line-height
  font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif; // Simplified font stack
  // text-size-adjust: 100%; // Often unnecessary unless targeting specific mobile issues
  width: fit-content; // Adjust width based on content
  min-width: 150px; // Example minimum width
}

.menu-item {
  display: flex;
  align-items: center;
  border-radius: $item-border-radius;
  box-sizing: border-box;
  cursor: pointer;
  min-height: 28px; // Keep minimum height
  padding: $item-padding;
  transition: background-color $transition-speed $transition-timing,
              color $transition-speed $transition-timing;
  user-select: none;
  // overflow: auto; // Usually not needed for simple items, causes scrollbars if content overflows padding
  white-space: nowrap; // Prevent text wrapping

  &:hover,
  &:focus-visible { // Add focus state for accessibility
    background-color: $hover-background;
    outline: none; // Remove default focus outline if custom style is applied
  }

  // Handle potential item-specific color override from props
  // The :style="{ color: item.color }" binding handles text color directly.
  // If icon should also inherit this color, it might need adjustment.
}

.menu-item-icon-wrapper {
  width: $icon-size;
  height: $icon-size;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $icon-margin-right;
  color: $icon-color; // Default icon color
  font-size: $icon-size; // Control icon size via font-size on wrapper

  // Allow item.color to override icon color if needed
  // This requires the :style binding on the parent .menu-item
  // and the Icon component using fill="currentColor"
  .menu-item[style*="color"] & { // If the parent has an inline color style
     color: inherit; // Inherit the color set inline
  }
}

.menu-item-icon {
   // Size is controlled by font-size on wrapper or within Icon.vue itself
   // width: $icon-size; // Redundant if controlled by font-size or Icon.vue
   // height: $icon-size; // Redundant
}

.menu-item-title {
  flex-grow: 1; // Allow title to take remaining space if needed
  line-height: 1.4; // Ensure text aligns well vertically within the flex container
}

// Specific style example (like the original red delete item)
// This can be handled by passing the color via props, e.g., color: 'rgb(239, 68, 68)'
// Alternatively, you could add specific classes via props if needed for more complex styling.
// Example: Add a class based on item type
// .menu-item--danger {
//   color: rgb(239, 68, 68);
//   .menu-item-icon-wrapper {
//      color: rgb(239, 68, 68); // Make icon red too
//   }
//   &:hover, &:focus-visible {
//     background-color: rgba(239, 68, 68, 0.1); // Reddish hover
//   }
// }
</style>