/**
 * 工具函数集合
 */

/**
 * 验证中国大陆手机号
 * 支持的格式：
 * - 11位数字，以1开头
 * - 常见的手机号段：13x, 14x, 15x, 16x, 17x, 18x, 19x
 * @param phoneNumber 手机号码
 * @returns 是否是有效的手机号
 */
export function validatePhoneNumber(phoneNumber: string): boolean {
  // 中国大陆手机号正则表达式
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phoneNumber);
}

/**
 * 验证邮箱地址
 * 支持的格式：
 * - 用户名部分：字母、数字、下划线、点、连字符
 * - @ 符号
 * - 域名部分：字母、数字、连字符、点
 * - 顶级域名：2-10个字母
 * @param email 邮箱地址
 * @returns 是否是有效的邮箱地址
 */
export function validateEmail(email: string): boolean {
  // 邮箱地址正则表达式
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,10}$/;
  return emailRegex.test(email);
}

/**
 * 验证账号（手机号或邮箱）
 * @param account 账号（手机号或邮箱）
 * @returns 是否是有效的账号
 */
export function validateAccount(account: string): boolean {
  return validatePhoneNumber(account) || validateEmail(account);
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本内容
 * @returns Promise<boolean> 复制是否成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  if (!text) {
    console.warn('复制内容为空');
    return false;
  }

  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级到传统的 execCommand 方法
      return fallbackCopyToClipboard(text);
    }
  } catch (error) {
    console.error('复制失败:', error);
    // 如果现代 API 失败，尝试降级方法
    return fallbackCopyToClipboard(text);
  }
}

/**
 * 降级的复制方法（兼容旧浏览器）
 * @param text 要复制的文本内容
 * @returns boolean 复制是否成功
 */
function fallbackCopyToClipboard(text: string): boolean {
  try {
    // 创建一个临时的 textarea 元素
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 设置样式使其不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';

    // 添加到 DOM
    document.body.appendChild(textArea);

    // 选择文本
    textArea.focus();
    textArea.select();

    // 执行复制命令
    const successful = document.execCommand('copy');

    // 清理：移除临时元素
    document.body.removeChild(textArea);

    return successful;
  } catch (error) {
    console.error('降级复制方法失败:', error);
    return false;
  }
}

/**
 * 复制消息内容并显示用户反馈
 * @param message 要复制的消息内容
 * @param onSuccess 复制成功时的回调函数
 * @param onError 复制失败时的回调函数
 */
export async function copyMessage(
  message: string,
  onSuccess?: () => void,
  onError?: (error: string) => void
): Promise<void> {
  if (!message) {
    const errorMsg = '没有内容可复制';
    console.warn(errorMsg);
    onError?.(errorMsg);
    return;
  }

  try {
    const success = await copyToClipboard(message);

    if (success) {
      console.log('消息复制成功');
      onSuccess?.();
    } else {
      const errorMsg = '复制失败，请手动复制';
      console.error(errorMsg);
      onError?.(errorMsg);
    }
  } catch (error) {
    const errorMsg = `复制过程中发生错误: ${error}`;
    console.error(errorMsg);
    onError?.(errorMsg);
  }
}