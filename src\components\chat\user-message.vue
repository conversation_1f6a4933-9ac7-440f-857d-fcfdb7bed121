<template>
  <div class="container" @mouseover="handleMouse(true)" @mouseout="handleMouse(false)">
    <div class="user-message">
      {{ message }}
    </div>
    <div class="message-actions-toolbar" :style="{ opacity: showShowToolbar ? '0.7' : '0' }">
      <div
        class="action-button copy-button"
        @click="copyMessage"
        :class="{
          'copying': copyStatus === 'copying',
          'success': copyStatus === 'success',
          'error': copyStatus === 'error'
        }"
        :title="copyStatus === 'success' ? '复制成功!' : copyStatus === 'error' ? copyErrorMessage : '复制消息'"
      >
        <div class="action-icon-wrapper">
          <IconCopy v-if="copyStatus === 'idle' || copyStatus === 'copying'" />
          <span v-else-if="copyStatus === 'success'" class="status-text">✓</span>
          <span v-else-if="copyStatus === 'error'" class="status-text">✗</span>
        </div>
      </div>
      <!-- <div class="action-button edit-button" @click="editMessage">
        <div class="action-icon-wrapper">
          <IconEdit />
        </div>
      </div> -->
      <VersionController v-if="showVersionController" v-bind="props.versions" @change-version="handleVersionChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { PropType } from 'vue';
import IconCopy from '../icons/IconCopy.vue';
// import IconEdit from '../icons/IconEdit.vue';
import VersionController from './version-controller.vue';
import { copyMessage as copyMessageUtil } from '../../util';

interface VersionControllerProps {
  current: number;
  total: number;
  loading?: boolean;
}

const props = defineProps({
  message: String,
  alwaysShowToolbar: Boolean,
  nodeId: String, // Add nodeId prop
  versions: {
    type: Object as PropType<VersionControllerProps>,
    required: false
  }
});

const showVersionController = computed(() => {
  return props.versions && props.versions.total > 1;
});

const showToolbar = ref(false);
// let showTimer: number | undefined = undefined;
// let hideTimer: number | undefined = undefined;

const showShowToolbar = computed(() => showToolbar.value || props.alwaysShowToolbar);

// 复制状态管理
const copyStatus = ref<'idle' | 'copying' | 'success' | 'error'>('idle');
const copyErrorMessage = ref('');

const copyMessage = async () => {
  if (!props.message) {
    return;
  }

  copyStatus.value = 'copying';

  await copyMessageUtil(
    props.message,
    // 成功回调
    () => {
      copyStatus.value = 'success';
      emit('copy', props.message);
      // 2秒后重置状态
      setTimeout(() => {
        copyStatus.value = 'idle';
      }, 2000);
    },
    // 失败回调
    (error: string) => {
      copyStatus.value = 'error';
      copyErrorMessage.value = error;
      // 3秒后重置状态
      setTimeout(() => {
        copyStatus.value = 'idle';
        copyErrorMessage.value = '';
      }, 3000);
    }
  );
};

// const editMessage = () => {
//   console.log('Edit action triggered');
//   if (props.nodeId) {
//     emit('edit-message', props.nodeId);
//   }
// };

let debounceTimer: number | undefined = undefined;

const emit = defineEmits(['change-version', 'edit-message', 'copy']);

const handleVersionChange = (newVersion: number) => {
  emit('change-version', newVersion);
};

const handleMouse = (show: boolean) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  debounceTimer = window.setTimeout(() => {
    showToolbar.value = show;
  }, 50);
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  display: flex;
  flex-direction: column;  // 调整为列布局，以便工具栏在消息下方
  align-items: flex-end;   // 保持右对齐
}

.user-message {
  display: inline-block;
  background-color: #eff6ff;
  padding: 12px 18px;
  border-radius: 10px;
  position: relative;
}

.message-actions-toolbar {
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0.7;
  transition: opacity 0.1s ease-in-out;
  margin-top: 5px;  // 添加一些间距，使其在消息下方
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: rgb(73, 73, 73);
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease, color 0.2s ease;
  padding: 2px;

  &:hover {
    color: #404040;
    background-color: #f5f5f5;
  }

  // 复制状态样式
  &.copying {
    opacity: 0.6;
    cursor: wait;
  }

  &.success {
    color: #22c55e; // 绿色表示成功
    background-color: #f0fdf4;
  }

  &.error {
    color: #ef4444; // 红色表示错误
    background-color: #fef2f2;
  }
}

.action-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  :deep(svg) {
    width: 18px;
    height: 18px;
    fill: currentColor;
  }

  // 状态文本样式
  .status-text {
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
  }
}
</style>
