// src/services/apiService.ts

import type {
  ChatSession,
  ChatSessionMetadata,
  ChatMessageNode,
  MessageTurnPayload,
  MessageTurnResponse,
  StreamEvent
} from '@/types/chat';

const API_BASE_URL = '/api/chat'; // Chat API endpoint

async function fetchApi<T>(url: string, options: RequestInit = {}): Promise<T> {
  const headers = {
    'Content-Type': 'application/json',
    // Add Authorization header if needed, e.g., for JWT
    // 'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
    ...options.headers,
  };
  const response = await fetch(`${API_BASE_URL}${url}`, { ...options, headers });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: response.statusText }));
    throw new Error(`API Error (${response.status}): ${errorData.message || 'Unknown error'}`);
  }
  if (response.status === 204) { // No Content
    return undefined as T;
  }
  return response.json() as T;
}

export const chatApiService = {
  createChat: (title: string = 'New Chat'): Promise<ChatSession> => {
    return fetchApi<ChatSession>('/chats', {
      method: 'POST',
      body: JSON.stringify({ title }),
    });
  },

  getChats: (): Promise<ChatSessionMetadata[]> => {
    return fetchApi<ChatSessionMetadata[]>('/chats');
  },

  getChatDetails: (chatId: string): Promise<ChatSession> => {
    return fetchApi<ChatSession>(`/chats/${chatId}`);
  },

  deleteChat: (chatId: string): Promise<{ message: string }> => {
    return fetchApi<{ message: string }>(`/chats/${chatId}`, {
      method: 'DELETE',
    });
  },

  renameChat: (chatId: string, newTitle: string): Promise<{ message: string, id: string, new_title: string }> => {
    return fetchApi<{ message: string, id: string, new_title: string }>(`/chats/${chatId}/rename`, {
      method: 'PUT',
      body: JSON.stringify({ title: newTitle }),
    });
  },

  postMessageTurn: (chatId: string, payload: MessageTurnPayload): Promise<MessageTurnResponse> => {
    return fetchApi<MessageTurnResponse>(`/chats/${chatId}/message_turns`, {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  },

  // 流式传输版本的消息发送
  postMessageTurnStream: async (
    chatId: string,
    payload: MessageTurnPayload,
    onMessage: (event: StreamEvent) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/chats/${chatId}/message_turns/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          if (onComplete) onComplete();
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一个不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonData = line.slice(6); // 移除 'data: ' 前缀
              if (jsonData.trim()) {
                const event = JSON.parse(jsonData) as StreamEvent;
                onMessage(event);

                if (event.type === 'message_complete' || event.type === 'error') {
                  if (onComplete) onComplete();
                  return;
                }
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', line, parseError);
            }
          }
        }
      }
    } catch (error) {
      if (onError) onError(error as Error);
    }
  },

  setActiveBranch: (chatId: string, nodeId: string, activeNextNodeId: string): Promise<{ message: string, updated_node_id: string, new_active_next_node_id: string }> => {
    return fetchApi<{ message: string, updated_node_id: string, new_active_next_node_id: string }>(`/chats/${chatId}/nodes/${nodeId}/set_active_branch`, {
      method: 'PUT',
      body: JSON.stringify({ active_next_node_id: activeNextNodeId }),
    });
  },

  setEntryNode: (chatId: string, entryNodeId: string): Promise<{ message: string, chat_id: string, entry_node_id: string, updated_at: string }> => {
    return fetchApi<{ message: string, chat_id: string, entry_node_id: string, updated_at: string }>(`/chats/${chatId}/entry_node`, {
      method: 'PUT',
      body: JSON.stringify({ entry_node_id: entryNodeId }),
    });
  },
};
