<template>
  <div v-html="htmlString" class="markdown-body"></div>
</template>

<script setup lang="ts">
import { marked } from 'marked';
import { computed } from 'vue';
import DOMPurify from 'dompurify';

const props = defineProps({
  markdown: String,
});

const htmlString = computed(() => {
  return DOMPurify.sanitize(marked.parse(props.markdown, { async: false }));
});
</script>

<style lang="scss">
// --- Variables ---
// 你可以在这里轻松修改颜色、字体、间距等

// Colors
$text-color: #333;
$link-color: #0366d6;
$link-hover-color: #005cc5;
$heading-color: #111;
$blockquote-border-color: #dfe2e5;
$blockquote-text-color: #6a737d;
$code-bg-color: #f6f8fa;
$code-text-color: #333;
$pre-bg-color: #f6f8fa;
$pre-text-color: #24292e;
$hr-color: #eaecef;
$table-border-color: #dfe2e5;
$table-header-bg: #f6f8fa;
$table-row-hover-bg: #f6f8fa;

// Fonts
$base-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "PingFang SC", "Microsoft YaHei", "Source Han Sans SC", "Noto Sans CJK SC";
$monospace-font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, Courier, monospace;

// Spacing & Sizes
$base-font-size: 16px;
$base-line-height: 1.7; // 适合中文阅读的行高
$spacing-unit: 16px;   // 基本间距单位
$container-max-width: 800px; // 内容最大宽度 (可选)

// --- Base Styles ---
.markdown-body {
  font-family: $base-font-family;
  font-size: $base-font-size;
  line-height: $base-line-height;
  color: $text-color;
  word-wrap: break-word; // 允许长单词或 URL 换行
  -webkit-font-smoothing: antialiased; // macOS/iOS 字体平滑
  -moz-osx-font-smoothing: grayscale;  // Firefox 字体平滑

  // 如果需要限制内容宽度并居中
  // max-width: $container-max-width;
  // margin: 0 auto;
  // padding: $spacing-unit * 2; // 给容器一些内边距

  // --- Headings ---
  h1, h2, h3, h4, h5, h6 {
    margin-top: $spacing-unit * 1.5;
    margin-bottom: $spacing-unit;
    font-weight: 600;
    line-height: 1.25;
    color: $heading-color;
    padding-bottom: 0.3em; // 给标题下方一点空间或用于边框
  }

  h1 {
    font-size: 2em; // 32px
    border-bottom: 1px solid $hr-color;
  }

  h2 {
    font-size: 1.5em; // 24px
    border-bottom: 1px solid $hr-color;
  }

  h3 {
    font-size: 1.25em; // 20px
  }

  h4 {
    font-size: 1em; // 16px
  }

  h5 {
    font-size: 0.875em; // 14px
  }

  h6 {
    font-size: 0.85em; // ~13.6px
    color: $blockquote-text-color;
  }

  // --- Paragraphs ---
  p {
    margin-top: 0;
    margin-bottom: $spacing-unit;
  }

  // --- Links ---
  a {
    color: $link-color;
    text-decoration: none;
    transition: color 0.15s ease-in-out;

    &:hover {
      color: $link-hover-color;
      text-decoration: underline;
    }
  }

  // --- Lists ---
  ul, ol {
    margin-top: 0;
    margin-bottom: $spacing-unit;
    padding-left: 2em; // 保持列表缩进

    ul, ol { // 嵌套列表
      margin-bottom: 0;
    }
  }

  li {
    margin-bottom: $spacing-unit * 0.25; // 列表项之间的小间距
    > p { // 列表项内的段落
      margin-bottom: $spacing-unit * 0.5;
    }
  }

  // --- Blockquotes ---
  blockquote {
    margin: 0 0 $spacing-unit 0;
    padding: 0 1em;
    color: $blockquote-text-color;
    border-left: 0.25em solid $blockquote-border-color;

    > :first-child {
      margin-top: 0;
    }
    > :last-child {
      margin-bottom: 0;
    }
  }

  // --- Code ---
  // Inline code
  code {
    font-family: $monospace-font-family;
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: $code-bg-color;
    border-radius: 3px;
    color: $code-text-color;
  }

  // Code blocks
  pre {
    font-family: $monospace-font-family;
    margin-top: 0;
    margin-bottom: $spacing-unit;
    padding: $spacing-unit;
    font-size: 85%;
    line-height: 1.45;
    background-color: $pre-bg-color;
    border-radius: 3px;
    overflow: auto; // 长代码行可滚动
    word-wrap: normal; // 代码不自动换行

    code {
      display: inline; // pre > code 不需要额外样式
      padding: 0;
      margin: 0;
      font-size: inherit; // 继承 pre 的字体大小
      line-height: inherit;
      background-color: transparent;
      border-radius: 0;
      color: $pre-text-color; // 使用 pre 的文本颜色
      overflow: visible;
      word-wrap: normal;
    }
  }

  // --- Horizontal Rules ---
  hr {
    height: 0.25em;
    padding: 0;
    margin: $spacing-unit * 1.5 0;
    background-color: $hr-color;
    border: 0;
    overflow: hidden; // 隐藏默认边框
  }

  // --- Images ---
  img {
    max-width: 100%;
    height: auto;
    box-sizing: content-box;
    background-color: #fff; // 如果图片是透明的，给个背景
    display: block; // 避免图片下方多余空隙
    margin: $spacing-unit auto; // 图片居中显示 (可选)
  }

  // --- Tables ---
  table {
    width: 100%; // 表格宽度充满容器 (可选)
    margin-top: 0;
    margin-bottom: $spacing-unit;
    border-collapse: collapse; // 合并边框
    border-spacing: 0;
    display: block; // 允许表格在小屏幕上滚动
    overflow: auto; // 表格内容过多时可滚动

    th, td {
      padding: $spacing-unit * 0.5 $spacing-unit * 0.75; // 单元格内边距
      border: 1px solid $table-border-color;
    }

    th {
      font-weight: 600;
      text-align: left; // 表头左对齐
      background-color: $table-header-bg;
    }

    // 可选：斑马条纹
    // tbody tr:nth-child(odd) {
    //   background-color: #fcfcfc;
    // }

    // 可选：鼠标悬停高亮行
    // tbody tr:hover {
    //   background-color: $table-row-hover-bg;
    // }
  }

  // --- Emphasis & Strong ---
  em {
    font-style: italic;
  }

  strong {
    font-weight: 600;
  }

  // --- Task Lists (GFM) ---
  // 如果你的 Markdown 解析器支持 GitHub Flavored Markdown 的任务列表
  .task-list-item {
    list-style-type: none; // 移除默认的项目符号
    input[type="checkbox"] {
      margin: 0 0.2em 0.25em -1.6em; // 调整复选框位置
      vertical-align: middle;
    }
  }
} // End of .markdown-body
</style>