import { defineStore } from 'pinia';
import type { User } from '@/types/api';

interface UserState {
  id: number | null;
  email: string | null;
  phone_number: string | null;
  is_admin: boolean;
}

/// @ts-expect-error 
export const useUserStore = defineStore('user', {
  state: () => ({
    id: null,
    email: null,
    phone_number: null,
    is_admin: false
  } as UserState),
  actions: {
    setUser(payload: User) {
      this.id = payload.id;
      this.email = payload.email;
      this.phone_number = payload.phone_number;
      this.is_admin = payload.is_admin || false;
    },
    logout() {
      this.id = null;
      this.email = null;
      this.phone_number = null;
      this.is_admin = false;
    }
  },
  persist: true
});