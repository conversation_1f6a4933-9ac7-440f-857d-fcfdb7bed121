# Toast 组件使用说明

Toast 组件基于 pop-menu 的设计模式，提供了全局的消息通知功能。

## 基本用法

```vue
<script setup>
import useToast from '@/components/toast/useToast';

const toast = useToast();

// 显示不同类型的消息
function showSuccess() {
  toast.success('操作成功！');
}

function showError() {
  toast.error('操作失败，请重试');
}

function showWarning() {
  toast.warning('请注意检查输入内容');
}

function showInfo() {
  toast.info('这是一条提示信息');
}
</script>

<template>
  <div>
    <button @click="showSuccess">成功消息</button>
    <button @click="showError">错误消息</button>
    <button @click="showWarning">警告消息</button>
    <button @click="showInfo">信息消息</button>
  </div>
</template>
```

## 高级用法

```vue
<script setup>
import useToast from '@/components/toast/useToast';

const toast = useToast();

// 自定义配置
function showCustomToast() {
  toast.show({
    type: 'success',
    message: '自定义消息',
    duration: 5000, // 5秒后自动消失
    closable: true, // 显示关闭按钮
  });
}

// 不自动消失的消息
function showPersistentToast() {
  const id = toast.error('这条消息不会自动消失', {
    duration: 0, // 0 表示不自动消失
    closable: true
  });
  
  // 可以手动关闭
  setTimeout(() => {
    toast.hide(id);
  }, 10000);
}

// 清除所有消息
function clearAllToasts() {
  toast.clear();
}
</script>
```

## API

### useToast()

返回一个包含以下方法的对象：

#### show(options: ToastOptions): string
显示一个 toast 消息，返回消息的唯一 ID。

**ToastOptions:**
- `type`: 'success' | 'error' | 'warning' | 'info' - 消息类型
- `message`: string - 消息内容
- `duration?`: number - 自动消失时间（毫秒），默认 3000，设为 0 则不自动消失
- `closable?`: boolean - 是否显示关闭按钮，默认 true
- `icon?`: Component - 自定义图标组件
- `id?`: string - 自定义 ID

#### success(message: string, options?: Partial<ToastOptions>): string
显示成功消息的便捷方法。

#### error(message: string, options?: Partial<ToastOptions>): string
显示错误消息的便捷方法。

#### warning(message: string, options?: Partial<ToastOptions>): string
显示警告消息的便捷方法。

#### info(message: string, options?: Partial<ToastOptions>): string
显示信息消息的便捷方法。

#### hide(id: string): void
隐藏指定 ID 的消息。

#### clear(): void
清除所有消息。

## ToastProvider 配置

在 Root.vue 中，ToastProvider 支持以下 props：

- `position`: 'top-right' | 'top-left' | 'top-center' | 'bottom-right' | 'bottom-left' | 'bottom-center' - 消息显示位置，默认 'top-right'
- `maxCount`: number - 最大同时显示的消息数量，默认 5

```vue
<template>
  <ToastProvider position="top-center" :max-count="3">
    <!-- 应用内容 -->
  </ToastProvider>
</template>
```

## 样式自定义

Toast 组件使用 SCSS 变量，可以通过覆盖这些变量来自定义样式：

```scss
// 自定义颜色
$success-color: #52c41a;
$error-color: #ff4d4f;
$warning-color: #faad14;
$info-color: #1890ff;

// 自定义尺寸
$toast-min-width: 300px;
$toast-max-width: 500px;
$toast-border-radius: 8px;
```
