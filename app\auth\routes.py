from flask import request, jsonify, current_app, make_response
from flask_login import login_user, logout_user, login_required, current_user
from app import db, login_manager # Import from top-level app package
from app.models import User, Conversation, ChatMessageNode
from app.auth import bp
from app.utils.helpers import validate_captcha
import json
from datetime import datetime

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    email = data.get('email')
    phone_number = data.get('phone_number')
    password = data.get('password')

    if not (email or phone_number) or not password:
        return jsonify({'error': 'Email or phone number and password are required'}), 400

    if email and User.query.filter_by(email=email).first():
        return jsonify({'error': 'Email already exists'}), 409

    if phone_number and User.query.filter_by(phone_number=phone_number).first():
        return jsonify({'error': 'Phone number already exists'}), 409

    user = User(email=email, phone_number=phone_number)
    user.set_password(password)
    db.session.add(user)
    db.session.commit()

    return jsonify({
        'message': 'User registered successfully',
        'user': {
            'id': user.id,
            'email': user.email,
            'phone_number': user.phone_number
        }
    }), 201

@bp.route('/login/password', methods=['POST'])
def login_password():
    data = request.get_json()
    identifier = data.get('identifier')
    password = data.get('password')

    if not identifier or not password:
        return jsonify({'error': 'Identifier and password are required'}), 400

    user = User.query.filter((User.email == identifier) | (User.phone_number == identifier)).first()

    if user and user.check_password(password):
        login_user(user, remember=data.get('remember_me', False))
        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': user.id,
                'email': user.email,
                'phone_number': user.phone_number,
                'is_admin': user.is_admin
            }
        }), 200

    return jsonify({'error': 'Invalid identifier or password'}), 401

@bp.route('/login/phone/send-otp', methods=['POST'])
def send_otp():
    data = request.get_json()
    phone_number = data.get('phone_number')

    if not phone_number:
        return jsonify({'error': 'Phone number is required'}), 400

    # TODO: Implement OTP sending logic
    # Generate OTP, store hash, send via SMS service
    return jsonify({'message': 'OTP sent successfully'}), 200

@bp.route('/login/phone/verify-otp', methods=['POST'])
def verify_otp():
    data = request.get_json()
    phone_number = data.get('phone_number')
    otp = data.get('otp')

    if not phone_number or not otp:
        return jsonify({'error': 'Phone number and OTP are required'}), 400

    # TODO: Implement OTP verification logic
    # Verify OTP, create user if not exists, login user
    user = User.query.filter_by(phone_number=phone_number).first()
    if not user:
        user = User(phone_number=phone_number)
        db.session.add(user)
        db.session.commit()

    login_user(user)
    return jsonify({
        'message': 'Login successful',
        'user': {
            'id': user.id,
            'phone_number': user.phone_number
        }
    }), 200

@bp.route('/logout', methods=['POST'])
@login_required
def logout():
    logout_user()
    return jsonify({'message': 'Logged out successfully'}), 200

@bp.route('/me', methods=['GET'])
@login_required
def get_current_user():
    return jsonify({
        'user': {
            'id': current_user.id,
            'email': current_user.email,
            'phone_number': current_user.phone_number,
            'email_verified': current_user.email_verified,
            'phone_verified': current_user.phone_verified,
            'is_admin': current_user.is_admin
        }
    }), 200

@bp.route('/password/forgot/request', methods=['POST'])
def forgot_password_request():
    data = request.get_json()
    identifier = data.get('identifier')

    if not identifier:
        return jsonify({'error': 'Email or phone number is required'}), 400

    user = User.query.filter((User.email == identifier) | (User.phone_number == identifier)).first()
    if user:
        # TODO: Implement password reset token generation and sending
        pass

    return jsonify({'message': 'If your account exists, password reset instructions have been sent'}), 200

@bp.route('/password/reset/token', methods=['POST'])
def reset_password_token():
    data = request.get_json()
    token = data.get('token')
    new_password = data.get('new_password')

    if not token or not new_password:
        return jsonify({'error': 'Token and new password are required'}), 400

    # TODO: Implement token verification and password update
    return jsonify({'message': 'Password has been reset successfully'}), 200


# 新增的四个API端点

@bp.route('/account', methods=['DELETE'])
@login_required
def delete_account():
    """
    注销账号 - 删除用户账号及其所有相关数据
    """
    try:
        user_id = current_user.id
        user_identifier = current_user.email or current_user.phone_number

        # 先登出当前用户
        logout_user()

        # 删除用户（级联删除会自动删除相关的对话和消息）
        user = User.query.get(user_id)
        if user:
            db.session.delete(user)
            db.session.commit()
            current_app.logger.info(f"User account deleted: {user_identifier}")

        return jsonify({'message': 'Account deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting account: {str(e)}")
        return jsonify({'error': 'Failed to delete account'}), 500


@bp.route('/conversations', methods=['DELETE'])
@login_required
def clear_all_conversations():
    """
    清空所有对话 - 删除当前用户的所有对话
    """
    try:
        # 删除当前用户的所有对话（级联删除会自动删除相关的消息节点）
        conversations = Conversation.query.filter_by(user_id=current_user.id).all()
        conversation_count = len(conversations)

        for conversation in conversations:
            db.session.delete(conversation)

        db.session.commit()
        current_app.logger.info(f"Cleared {conversation_count} conversations for user {current_user.id}")

        return jsonify({
            'message': 'All conversations cleared successfully',
            'deleted_count': conversation_count
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error clearing conversations: {str(e)}")
        return jsonify({'error': 'Failed to clear conversations'}), 500


@bp.route('/logout-all', methods=['POST'])
@login_required
def logout_all_devices():
    """
    登出所有设备 - 使所有设备上的会话失效
    注意：由于Flask-Login基于session的特性，这里主要是登出当前会话
    如果需要真正的多设备管理，需要实现基于token的认证系统
    """
    try:
        user_identifier = current_user.email or current_user.phone_number

        # 登出当前用户
        logout_user()

        # 在实际的多设备管理中，这里应该：
        # 1. 使所有JWT token失效（如果使用JWT）
        # 2. 清除所有设备的session记录
        # 3. 通知其他设备登出

        current_app.logger.info(f"User logged out from all devices: {user_identifier}")

        return jsonify({'message': 'Logged out from all devices successfully'}), 200

    except Exception as e:
        current_app.logger.error(f"Error logging out from all devices: {str(e)}")
        return jsonify({'error': 'Failed to logout from all devices'}), 500


@bp.route('/export/conversations', methods=['GET'])
@login_required
def export_conversations():
    """
    导出所有历史对话 - 导出用户的所有对话数据为JSON格式
    """
    try:
        # 获取当前用户的所有对话
        conversations = Conversation.query.filter_by(user_id=current_user.id).order_by(Conversation.created_at.desc()).all()

        export_data = {
            'user_info': {
                'id': current_user.id,
                'email': current_user.email,
                'phone_number': current_user.phone_number,
                'export_date': datetime.utcnow().isoformat() + 'Z'
            },
            'conversations': []
        }

        for conversation in conversations:
            # 获取对话的完整数据
            conversation_data = conversation.to_dict_full()
            export_data['conversations'].append(conversation_data)

        # 创建响应
        response_data = json.dumps(export_data, ensure_ascii=False, indent=2)
        response = make_response(response_data)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename="conversations_export_{current_user.id}_{datetime.utcnow().strftime("%Y%m%d_%H%M%S")}.json"'

        current_app.logger.info(f"Exported {len(conversations)} conversations for user {current_user.id}")

        return response

    except Exception as e:
        current_app.logger.error(f"Error exporting conversations: {str(e)}")
        return jsonify({'error': 'Failed to export conversations'}), 500