<template>
  <div class="toast-container" :class="`toast-container--${position}`">
    <TransitionGroup name="toast" tag="div">
      <Toast
        v-for="toast in toasts"
        :key="toast.id"
        :item="toast"
        @close="hide(toast.id)"
      />
    </TransitionGroup>
  </div>
  <slot></slot>
</template>

<script lang="ts" setup>
import { provide, shallowRef } from 'vue';
import Toast from './toast.vue';
import type { ToastItem } from './toast.vue';
import { IToastHookReturn, ToastSymbol, type ToastOptions } from './useToast';

const props = defineProps({
  position: {
    type: String as () => 'top-right' | 'top-left' | 'top-center' | 'bottom-right' | 'bottom-left' | 'bottom-center',
    default: 'top-right'
  },
  maxCount: {
    type: Number,
    default: 5
  }
});

const toasts = shallowRef<ToastItem[]>([]);
const timers = new Map<string, number>();

// 生成唯一 ID
function generateId(): string {
  return `toast_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// 显示 toast
function show(options: ToastOptions): string {
  const id = options.id || generateId();

  const toastItem: ToastItem = {
    id,
    type: options.type,
    message: options.message,
    duration: options.duration ?? 3000,
    closable: options.closable ?? true,
    icon: options.icon
  };

  // 如果超过最大数量，移除最旧的
  if (toasts.value.length >= props.maxCount) {
    const oldestToast = toasts.value[0];
    hide(oldestToast.id);
  }

  toasts.value.push(toastItem);

  // 设置自动消失定时器
  if (toastItem.duration && toastItem.duration > 0) {
    const timer = window.setTimeout(() => {
      hide(id);
    }, toastItem.duration);
    timers.set(id, timer);
  }

  return id;
}

// 隐藏指定 toast
function hide(id: string): void {
  const index = toasts.value.findIndex(toast => toast.id === id);
  if (index > -1) {
    toasts.value.splice(index, 1);
  }

  // 清除定时器
  const timer = timers.get(id);
  if (timer) {
    clearTimeout(timer);
    timers.delete(id);
  }
}

// 清除所有 toast
function clear(): void {
  toasts.value = [];
  // 清除所有定时器
  timers.forEach(timer => clearTimeout(timer));
  timers.clear();
}

// 便捷方法
function success(message: string, options: Partial<ToastOptions> = {}): string {
  return show({ type: 'success', message, ...options });
}

function error(message: string, options: Partial<ToastOptions> = {}): string {
  return show({ type: 'error', message, ...options });
}

function warning(message: string, options: Partial<ToastOptions> = {}): string {
  return show({ type: 'warning', message, ...options });
}

function info(message: string, options: Partial<ToastOptions> = {}): string {
  return show({ type: 'info', message, ...options });
}

// 提供给子组件使用
const provideData: IToastHookReturn = {
  show,
  success,
  error,
  warning,
  info,
  hide,
  clear
};

provide(ToastSymbol, provideData);
</script>

<style lang="scss">
.toast-container {
  position: fixed;
  z-index: 9999;
  pointer-events: none;

  &--top-right {
    top: 20px;
    right: 20px;
  }

  &--top-left {
    top: 20px;
    left: 20px;
  }

  &--top-center {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  &--bottom-right {
    bottom: 20px;
    right: 20px;
  }

  &--bottom-left {
    bottom: 20px;
    left: 20px;
  }

  &--bottom-center {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  > div {
    pointer-events: auto;
  }
}

// Toast 动画
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}

// 针对不同位置的动画
.toast-container--top-left,
.toast-container--bottom-left {
  .toast-enter-from,
  .toast-leave-to {
    transform: translateX(-100%);
  }
}

.toast-container--top-center,
.toast-container--bottom-center {
  .toast-enter-from,
  .toast-leave-to {
    transform: translateY(-20px);
  }
}
</style>
