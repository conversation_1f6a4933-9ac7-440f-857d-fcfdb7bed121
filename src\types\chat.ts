/**
 * @fileoverview 用于 LLM 聊天记录的 TypeScript 定义，采用线性的消息列表。
 */

/**
 * 表示与 LLM 生成的消息或用户消息相关的元数据。
 */
export interface MessageMetadata {
  /** 如果消息来自 assistant，则为所使用的模型，例如 "model-a"。 */
  model?: string;
  /** 用户对 assistant 消息的反馈，例如 "good", "bad"。 */
  feedback?: string;
  /** 允许其他任意元数据。 */
  [key: string]: any;
}

/**
 * 代表对话中的一个“消息节点”。
 * 每个节点代表用户的一次输入或 LLM 的一次回应。
 */
export interface ChatMessageNode {
  /** 此消息节点的唯一标识符。 */
  node_id: string;
  /** 消息节点的角色，可以是 "user" 或 "assistant"。 */
  role: "user" | "assistant";
  /**
   * 此消息节点的文本内容。
   */
  content: string;
  /**
   * ISO date-time string 指示此消息节点的创建时间。
   */
  timestamp: string;
  /**
   * 关于此消息节点的可选元数据。
   */
  metadata?: MessageMetadata;
  // next_node_ids 和 active_next_node_id 已移除
}

/**
 * 表示一个完整的聊天会话。
 */
export interface ChatSession {
  /** Chat 会话的唯一标识符。 */
  id: string;
  /** Chat 会话的可选标题。 */
  title?: string;
  /** ISO date-time string 指示 Chat 会话的创建时间。 */
  created_at: string; // 对应后端的 created_at
  /** ISO date-time string 指示 Chat 会话的最后更新时间。 */
  updated_at: string; // 对应后端的 updated_at
  /**
   * 包含此聊天会话中所有消息节点的列表（按时间顺序排列）。
   */
  message_nodes: ChatMessageNode[];
  // entry_node_id 已移除
}

// 用于列出对话的元数据
export interface ChatSessionMetadata {
  id: string;
  title?: string;
  created_at: string;
  updated_at: string;
  // entry_node_id 已移除
}

// 发送新消息的载荷
export interface MessageTurnPayload {
  // origin_node_id 已移除
  // action_type 仅为 'new_message'，可不在载荷中明确，或由后端固定
  user_content: string; // 对于新消息，内容是必需的
  metadata?: Record<string, any>; // 用户消息的可选元数据
}

// 发送消息后的响应
export interface MessageTurnResponse {
  new_user_node: ChatMessageNode; // 不再是 nullable
  new_assistant_node: ChatMessageNode;
  // updated_origin_node_links 已移除
  chat_session_meta_update: {
    id: string;
    updated_at: string;
    // entry_node_id 已移除
  };
}

// 流式传输事件类型
export interface StreamEvent {
  type: 'user_message' | 'assistant_message_start' | 'content_chunk' | 'message_complete' | 'error';
  data: any;
}

// 用户消息事件数据
export interface UserMessageEventData {
  type: 'user_message';
  data: ChatMessageNode;
}

// 助手消息开始事件数据
export interface AssistantMessageStartEventData {
  type: 'assistant_message_start';
  data: ChatMessageNode;
}

// 内容块事件数据
export interface ContentChunkEventData {
  type: 'content_chunk';
  data: {
    node_id: string;
    chunk: string;
  };
}

// 消息完成事件数据
export interface MessageCompleteEventData {
  type: 'message_complete';
  data: {
    assistant_node: ChatMessageNode;
    chat_session_meta_update: {
      id: string;
      updated_at: string;
    };
  };
}

// 错误事件数据
export interface ErrorEventData {
  type: 'error';
  data: {
    message: string;
  };
}