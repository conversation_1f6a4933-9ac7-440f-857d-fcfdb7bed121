﻿﻿<script setup>
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click', 'update:modelValue']);

function handleClick() {
  if (!props.disabled) {
    emit('update:modelValue', !props.modelValue);
    emit('click');
  }
}
</script>

<template>
  <div
    role="button"
    tabindex="0"
    class="action-button"
    :class="{ active: modelValue }"
    :aria-disabled="disabled"
    @click="handleClick"
  >
    <div class="icon-wrapper">
      <span class="icon-container">
        <div class="icon-inner">
          <slot name="icon"></slot>
        </div>
      </span>
    </div>
    <span class="button-text">
      <slot name="text"></slot>
    </span>
  </div>
</template>

<style scoped lang="scss">
.action-button {
  align-items: center;
  border-radius: 14px;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  font-size: 14px;
  font-variant-numeric: tabular-nums;
  height: 28px;
  line-height: 25px;
  outline: rgb(76, 76, 76) none 0px;
  padding: 0px 7px;
  position: relative;
  text-decoration: none solid rgb(76, 76, 76);
  white-space: nowrap;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  background-color: rgb(255, 255, 255);
  color: rgb(76, 76, 76);
  border: 0.666667px solid rgba(0, 0, 0, 0.12);
  justify-content: center;
  margin-right: 10px;
  font-family: DeepSeek-CJK-patch, Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif;

  &:not([aria-disabled="true"]):hover {
    background-color: rgb(224, 228, 237);
  }

  &.active {
    background-color: rgb(195, 218, 248);
    color: rgb(77, 107, 254);
    border: 0.666667px solid rgba(0, 122, 255, 0.15);
    outline-color: rgb(77, 107, 254);
    text-decoration-color: rgb(77, 107, 254);

    .icon-inner {
      color: rgb(77, 107, 254);
    }
  }

  &[aria-disabled="true"] {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.icon-wrapper {
  align-items: center;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  font-size: 18px;
  height: 18px;
  justify-content: center;
  line-height: 0px;
  margin: 0px 4px 0px 0px;
  width: 18px;
  color: rgb(255, 255, 255);
}

.icon-container {
  transition: transform 0.4s ease-out;
  transform: matrix(1, 0, 0, 1, 0, 0);
}

.icon-inner {
  display: inline-flex;
  line-height: 0px;
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: rgb(76, 76, 76);
}

.button-text {
  font-size: 12px;
  line-height: 17px;
}
</style>
