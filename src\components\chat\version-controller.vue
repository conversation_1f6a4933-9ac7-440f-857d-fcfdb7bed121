<template>
  <div class="version-control-wrapper">
    <div role="button"
         class="version-button"
         :class="{ 'disabled': !canGoPrevious }"
         @click="goToPrevious"
         :aria-disabled="!canGoPrevious"
         :tabindex="canGoPrevious ? 0 : -1">
      <div class="version-icon">
        <IconArrowLeft />
      </div>
    </div>
    <div class="version-text">{{ current }} / {{ total }}</div>
    <div role="button"
         class="version-button"
         :class="{ 'disabled': !canGoNext }"
         @click="goToNext"
         :aria-disabled="!canGoNext"
         :tabindex="canGoNext ? 0 : -1">
      <div class="version-icon">
        <IconArrowRight />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import IconArrowLeft from '../icons/IconArrowLeft.vue';
import IconArrowRight from '../icons/IconArrowRight.vue';

interface VersionControllerProps {
  current: number;
  total: number;
  loading?: boolean;
}

const props = defineProps<VersionControllerProps>();

const emit = defineEmits(['change-version']);

const canGoPrevious = computed(() => {
  return props.current > 1 && !props.loading;
});

const canGoNext = computed(() => {
  return props.current < props.total && !props.loading;
});

const goToPrevious = () => {
  if (canGoPrevious.value) {
    emit('change-version', props.current - 1);
  }
};

const goToNext = () => {
  if (canGoNext.value) {
    emit('change-version', props.current + 1);
  }
};
</script>

<style lang="scss" scoped>
.version-control-wrapper {
  display: flex;
  align-items: center;
  gap: 2px;
  color: rgb(38, 38, 38);
  font-size: 16px;
  line-height: 28px;
  white-space: pre-wrap;
  text-size-adjust: 100%;
  font-family: DeepSeek-CJK-patch, Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif;
}

.version-button {
  align-items: center;
  border-radius: 8px;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  font-size: 10px;
  height: 24px;
  line-height: 18px;
  outline: none;
  position: relative;
  text-decoration: none solid rgb(139, 139, 139);
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0);
  color: rgb(139, 139, 139);
  width: 24px;

  &:hover:not(.disabled) {
    background-color: rgba(0, 0, 0, 0.05); // Subtle hover effect
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.45;
    color: rgb(139, 139, 139); // Ensure disabled color is consistent
  }

  &:focus-visible:not(.disabled) {
    outline: 2px solid dodgerblue;
    outline-offset: 1px;
  }
}

.version-icon {
  display: flex;
  line-height: 0px;
  font-size: 20px; // Matched from original .DIV-2 / .DIV-5
  width: 20px;
  height: 20px;

  :deep(svg) { // Ensure SVG inside icon components inherits color
    fill: currentColor;
    width: 20px; // Matched from original svg-0 / svg-1
    height: 20px; // Matched from original svg-0 / svg-1
  }
}

.version-text {
  color: rgb(139, 139, 139);
  font-size: 15px; // Matched from original .DIV-3
  font-variant-numeric: tabular-nums;
  font-weight: 500;
  line-height: 18px; // Matched from original .DIV-3
  white-space: nowrap;
  user-select: none;
  padding: 0 4px; // Add some padding around the text
}
</style>
