from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid # 用于生成 node_id

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=True)
    phone_number = db.Column(db.String(20), unique=True, nullable=True)
    password_hash = db.Column(db.String(256))
    is_admin = db.Column(db.<PERSON><PERSON>an, default=False)
    email_verified = db.Column(db.<PERSON>, default=False)
    phone_verified = db.Column(db.Boolean, default=False)
    conversations = db.relationship('Conversation', backref='user', lazy='dynamic', cascade="all, delete-orphan")

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.email or self.phone_number}>'

class Conversation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(150), default='New Chat')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联所有的消息节点
    message_nodes = db.relationship('ChatMessageNode', backref='conversation', lazy='dynamic', cascade="all, delete-orphan", order_by="ChatMessageNode.timestamp") # 添加默认排序

    def __repr__(self):
        return f'<Conversation {self.id} - {self.title}>'

    def to_dict_metadata(self): # 用于列表，不含 message_nodes
        return {
            'id': str(self.id),
            'title': self.title,
            'created_at': self.created_at.isoformat() + 'Z',
            'updated_at': self.updated_at.isoformat() + 'Z',
        }

    def to_dict_full(self): # 用于获取单个会话详情
        # message_nodes 将根据 relationship 中的 order_by 自动排序
        nodes = [node.to_dict() for node in self.message_nodes.all()]
        return {
            'id': str(self.id),
            'title': self.title,
            'created_at': self.created_at.isoformat() + 'Z',
            'updated_at': self.updated_at.isoformat() + 'Z',
            'message_nodes': nodes
        }

class ChatMessageNode(db.Model):
    node_id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversation.id'), nullable=False)

    role = db.Column(db.String(20), nullable=False)  # 'user' or 'assistant'
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True) # 添加索引以优化排序

    message_metadata = db.Column(db.JSON, nullable=True)

    # next_node_ids 和 active_next_node_id 已移除

    def __repr__(self):
        return f'<ChatMessageNode {self.node_id} from {self.role}>'

    def to_dict(self):
        timestamp_str = self.timestamp.isoformat() + 'Z' if self.timestamp else datetime.utcnow().isoformat() + 'Z'

        return {
            'node_id': self.node_id,
            'role': self.role,
            'content': self.content,
            'timestamp': timestamp_str,
            'metadata': self.message_metadata or {},
            # 'next_node_ids' 和 'active_next_node_id' 已移除
        }

class SystemSetting(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.String(500))

    def __repr__(self):
        return f'<SystemSetting {self.key}: {self.value}>'